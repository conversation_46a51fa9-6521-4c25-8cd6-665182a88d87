// Happy path E2E for Team Management using real endpoints where possible

describe('Team Management - Auth and Navigation (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';

  beforeEach(() => {
    // Perform a real UI login to obtain valid Cognito tokens
    const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
  });

  it('navigates to Team Management and shows tabs', () => {
    cy.visit('/team');

    // Header

    cy.contains(
      'Manage your team members, roles, and permissions with powerful collaboration tools',
    ).should('be.visible');

    // Tabs
    cy.contains('Team Members').should('be.visible');
    cy.contains('Roles & Permissions').should('be.visible');
    cy.contains('Activity Log').should('be.visible');
  });

  it('shows team statistics dashboard', () => {
    cy.visit('/team');

    // Team statistics should be visible
    cy.findByTestId('total-members-stats-card').should('be.visible');
    cy.findByTestId('active-users-stats-card').should('be.visible');
    cy.findByTestId('pending-invites-stats-card').should('be.visible');
    cy.findByTestId('roles-stats-card').should('be.visible');
  });

  it('displays team members table with actions', () => {
    // Stub team members data
    cy.intercept('GET', '/api/orgs/*/users*', {
      statusCode: 200,
      body: {
        success: true,
        users: [
          {
            id: 'user1',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            role: 'Member',
            status: 'active',
            invitedAt: new Date().toISOString(),
            lastLogin: new Date().toISOString(),
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            firstName: 'Jane',
            lastName: 'Smith',
            role: 'Admin',
            status: 'active',
            invitedAt: new Date().toISOString(),
            lastLogin: new Date().toISOString(),
          },
        ],
        pagination: {
          currentPage: 0,
          limit: 50,
          total: 2,
        },
      },
    }).as('getUsers');

    cy.visit('/team');

    // Team members table should be visible
    cy.findByTestId('team-members-table').should('be.visible');

    // Check for member rows
    cy.findByTestId('member-row-user').should('be.visible');

    // Check action buttons
    cy.findByTestId('change-role-btn').should('be.visible');
    cy.findByTestId('remove-user-btn').should('be.visible');
  });

  it('opens invite member modal', () => {
    cy.visit('/team');

    // Click invite button
    cy.findByTestId('invite-member-btn').click();

    // Modal should be visible
    cy.findByTestId('invite-member-modal').should('be.visible');
    cy.findByTestId('invite-email-input').should('be.visible');
    cy.findByTestId('invite-role-select').should('be.visible');
    cy.findByTestId('send-invite-btn').should('be.visible');

    // Check tabs in modal

    cy.contains('Multiple').should('be.visible');
    cy.contains('Bulk').should('be.visible');
  });

  it('shows roles and permissions tab', () => {
    cy.visit('/team');
    cy.contains('Roles & Permissions').click();

    // Roles tab should be visible
    cy.findByTestId('roles-permissions-container').should('be.visible');

    // Should show role definitions
    cy.contains('Owner').should('be.visible');
    cy.contains('Admin').should('be.visible');
    cy.contains('Member').should('be.visible');
  });

  it('displays activity log with filtering', () => {
    cy.visit('/team');
    cy.contains('Activity Log').click();

    // Activity log should be visible
    cy.findByTestId('activity-log-container').should('be.visible');

    cy.findByTestId('activity-search-input').should('be.visible');

    // Should have activity entries
    cy.findByTestId('activity-log-table').should('be.visible');
  });

  it('handles search functionality', () => {
    cy.visit('/team');

    // Enter search term
    cy.findByTestId('team-search-input').type('alex').type('{enter}');

    // Should show filtered results
    cy.findByTestId('member-row-user').should('be.visible');

  });
});
