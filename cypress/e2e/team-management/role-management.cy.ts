// Happy path E2E for Team Role Management using real endpoints

describe('Team Management - Role Management (happy path)', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
  });

  // //working
  // it('filters team members by role', () => {
  //   cy.visit('/team');

  //   // Wait for team to load completely
  //   cy.findByTestId('team-members-table').should('be.visible');

  //   // Wait for data to load - ensure we have team members
  //   cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);

  //   // Wait a bit more for the table to fully render
  //   cy.wait(2000);

  //   // Get initial count
  //   cy.get('[data-testid="member-row-user"]').then(($rows) => {
  //     const initialCount = $rows.length;
  //     cy.log(`Initial team member count: ${initialCount}`);
  //   });

  //   // Filter by specific role - click on the trigger button, not the value
  //   cy.findByTestId('role-filter-select-trigger').click();

  //   // Wait for dropdown to open and try to find any admin role (case insensitive)
  //   cy.get('[role="listbox"]').should('be.visible');

  //   // Try to find admin role in various formats
  //   cy.get('body').then(($body) => {
  //     const options = $body.find('[role="option"]');
  //     let adminFound = false;

  //     // Look for admin in different cases
  //     options.each((_, option) => {
  //       const text = Cypress.$(option).text().toLowerCase();
  //       if (text.includes('admin')) {
  //         cy.wrap(option).click();
  //         adminFound = true;
  //         cy.log(`Found and clicked admin option: ${Cypress.$(option).text()}`);
  //         return false; // break the loop
  //       }
  //     });

  //     if (!adminFound) {
  //       // If no admin found, just select the first non-"All Roles" option
  //       cy.get('[role="option"]').not(':contains("All Roles")').first().click();
  //       cy.log('No admin role found, selected first available role for testing');
  //     }
  //   });

  //   // Wait for filter to apply
  //   cy.wait(2000);

  //   // Should show filtered results (may be 0 if no users with that role)
  //   cy.get('body').then(($body) => {
  //     if ($body.find('[data-testid="member-row-user"]').length > 0) {
  //       cy.get('[data-testid="member-row-user"]').should('exist');
  //       cy.log('Filtered users found and displayed');
  //     } else {
  //       cy.log('No users found with selected role - this is acceptable');
  //     }
  //   });

  //   // Reset filter - click trigger again to open dropdown
  //   cy.findByTestId('role-filter-select-trigger').click();
  //   cy.get('[role="option"]').contains('All Roles').click();

  //   // Wait for reset to apply and verify we're back to showing all users
  //   cy.wait(1000);
  //   cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);
  // });
  // //working
  // it('handles insufficient permissions gracefully', () => {
  //   // This test simulates what happens when a user tries to perform actions they don't have permission for
  //   cy.visit('/team');

  //   // Check if change role buttons exist (they may not for users with insufficient permissions)
  //   cy.get('body').then(($body) => {
  //     if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
  //       // If buttons exist, test that they're properly disabled/enabled based on permissions
  //       cy.get('[data-testid*="change-role-btn"]')
  //         .first()
  //         .then(($btn) => {
  //           // Button might be disabled for certain roles
  //           if ($btn.is(':disabled')) {
  //             cy.log('Change role button is properly disabled for insufficient permissions');
  //           } else {
  //             cy.log('Change role button is enabled (user has sufficient permissions)');
  //           }
  //         });
  //     } else {
  //       cy.log('No change role buttons visible (user may not have permission)');
  //     }
  //   });
  // });

  it('validates role change inputs', () => {
    cy.visit('/team');

    // Wait for team to load
    cy.findByTestId('team-members-table').should('be.visible');

    // Wait for data to load - ensure we have team members
    cy.get('[data-testid="member-row-user"]').should('have.length.greaterThan', 0);

    // Wait for the table to fully render
    cy.wait(2000);

    // Test role change validation
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
        // Click the first available change role button
        cy.get('[data-testid*="change-role-btn"]').first().click();

        // Wait for modal to open
        cy.contains('Change User Role').should('be.visible');
        cy.contains('Update permissions for').should('be.visible');

        // Get the current role and available roles dynamically
        cy.get('body').then(($body) => {
          const modalText = $body.text();
          const emailMatch = modalText.match(/Update permissions for\s+([^\s]+@[^\s]+)/);
          const userEmail = emailMatch ? emailMatch[1] : '';
          cy.log(`Found user email from modal: ${userEmail}`);

          // Find the current role (marked with "Current Role")
          cy.get('body').then(($modalBody) => {
            const currentRoleElement = $modalBody.find(':contains("Current Role")').closest('div');
            let currentRole = '';

            if (currentRoleElement.length > 0) {
              // Extract current role name
              const roleText = currentRoleElement.text();
              if (roleText.includes('Admin')) currentRole = 'Admin';
              else if (roleText.includes('Manager')) currentRole = 'Manager';
              else if (roleText.includes('Viewer')) currentRole = 'Viewer';
              else if (roleText.includes('Owner')) currentRole = 'Owner';
              else if (roleText.includes('Member')) currentRole = 'Member';
            }

            cy.log(`Current role: ${currentRole}`);

            // Select a different role dynamically
            const availableRoles = ['Viewer', 'Manager', 'Admin', 'Member'];
            const targetRole = availableRoles.find((role) => role !== currentRole);

            if (targetRole) {
              cy.log(`Selecting new role: ${targetRole}`);
              cy.contains(targetRole).click();

              // Confirm the change
              cy.get('button').contains('Confirm').click();

              // Should show success or handle the role change
              cy.log(`Role change from ${currentRole} to ${targetRole} attempted`);
            } else {
              cy.log('No different role available to select');
              // Cancel the modal
              cy.get('button').contains('Cancel').click();
            }
          });
        });
      } else {
        cy.log('No change role buttons found - user may not have permissions');
      }
    });
  });

  // it('handles API errors during role changes', () => {
  //   // Test error handling when API fails
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'user1',
  //           email: '<EMAIL>',
  //           firstName: 'Error',
  //           lastName: 'User',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 1,
  //       },
  //     },
  //   }).as('getUsers');

  //   // Simulate API error
  //   cy.intercept('PUT', '/api/orgs/users/update', {
  //     statusCode: 500,
  //     body: {
  //       success: false,
  //       message: 'Internal server error',
  //     },
  //   }).as('updateRoleError');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Try to change role and handle error
  //   cy.get('body').then(($body) => {
  //     if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
  //       cy.findByTestId('change-role-btn-user1').click();

  //       // Select new role
  //       cy.get('select').select('Admin');

  //       // Try to confirm
  //       cy.get('button').contains('Confirm').click();

  //       // Wait for error response
  //       cy.wait('@updateRoleError');

  //       // Should show error message
  //       cy.contains('Internal server error').should('be.visible');
  //     }
  //   });
  // });

  // it('tests role permission boundaries', () => {
  //   cy.visit('/team');
  //   cy.contains('Roles & Permissions').click();

  //   // Test that higher roles have more permissions
  //   const rolePermissions = {
  //     Owner: ['Full control', 'Organization management'],
  //     Admin: ['Team management', 'User management'],
  //     Manager: ['Team management'],
  //     Member: ['Basic access'],
  //     Viewer: ['Read-only access'],
  //   };

  //   // Check that each role has appropriate permissions
  //   Object.entries(rolePermissions).forEach(([role, permissions]) => {
  //     cy.contains(role).should('be.visible');
  //     permissions.forEach((permission) => {
  //       cy.contains(role)
  //         .parents('[data-testid="roles-permissions-container"]')
  //         .contains(permission)
  //         .should('be.visible');
  //     });
  //   });
  // });

  // it('handles role change cancellation', () => {
  //   // Test with stubbed data for consistent behavior
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'user1',
  //           email: '<EMAIL>',
  //           firstName: 'Cancel',
  //           lastName: 'User',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 1,
  //       },
  //     },
  //   }).as('getUsers');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Test role change cancellation
  //   cy.get('body').then(($body) => {
  //     if ($body.find('[data-testid*="change-role-btn"]').length > 0) {
  //       cy.findByTestId('change-role-btn-user1').click();

  //       // Modal should be visible
  //       cy.get('[data-testid*="role-modal"]').should('be.visible');

  //       // Select new role
  //       cy.get('select').select('Admin');

  //       // Cancel the operation
  //       cy.get('button').contains('Cancel').click();

  //       // Modal should be closed
  //       cy.get('[data-testid*="role-modal"]').should('not.exist');

  //       // User role should remain unchanged
  //       cy.contains('<EMAIL>')
  //         .parents('[data-testid="member-row-user"]')
  //         .contains('Member')
  //         .should('be.visible');
  //     }
  //   });
  // });

  // it('tests role filtering and sorting', () => {
  //   cy.visit('/team');

  //   // Wait for team to load
  //   cy.findByTestId('team-members-table').should('be.visible');

  //   // Test role filter
  //   cy.get('select')
  //     .filter((_, el) => Cypress.$(el).text().includes('All Roles'))
  //     .as('roleFilter');

  //   // Test each role option
  //   const roles = ['All Roles', 'Owner', 'Admin', 'Manager', 'Member', 'Viewer'];

  //   roles.forEach((role) => {
  //     cy.get('@roleFilter').click();
  //     cy.get('[role="option"]').contains(role).click();
  //     cy.wait(500); // Wait for filter to apply

  //     // Should show table (may be empty)
  //     cy.findByTestId('team-members-table').should('be.visible');
  //   });

  //   // Test sorting by role column
  //   cy.contains('tr', 'Role').click(); // Click role column header
  //   cy.wait(500); // Wait for sort to apply

  //   // Table should still be visible
  //   cy.findByTestId('team-members-table').should('be.visible');
  // });

  // it('changes member role successfully', () => {
  //   // Stub initial users
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'user1',
  //           email: '<EMAIL>',
  //           firstName: 'John',
  //           lastName: 'Doe',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 1,
  //       },
  //     },
  //   }).as('getUsers');

  //   // Stub role update
  //   cy.intercept('PUT', '/api/orgs/users/update', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       message: 'Role updated successfully',
  //       user: {
  //         id: 'user1',
  //         email: '<EMAIL>',
  //         firstName: 'John',
  //         lastName: 'Doe',
  //         role: 'Admin',
  //         status: 'active',
  //         updatedAt: new Date().toISOString(),
  //       },
  //     },
  //   }).as('updateRole');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Open role change dialog
  //   cy.findByTestId('change-role-btn-user1').click();

  //   // Role change modal should be visible
  //   cy.findByTestId('change-role-modal').should('be.visible');
  //   cy.findByTestId('current-role-display').should('contain', 'Member');
  //   cy.findByTestId('new-role-select').should('be.visible');

  //   // Select new role
  //   cy.findByTestId('new-role-select').select('Admin');

  //   // Confirm change
  //   cy.findByTestId('confirm-role-change-btn').click();

  //   // Verify API call
  //   cy.wait('@updateRole').its('request.body').should('deep.include', {
  //     userId: 'user1',
  //     newRole: 'Admin',
  //   });

  //   // Should show success message
  //   cy.contains('Role updated successfully').should('be.visible');

  //   // Table should reflect new role
  //   cy.findByTestId('member-role-user1').should('contain', 'Admin');
  // });

  // it('removes team member with confirmation', () => {
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'user1',
  //           email: '<EMAIL>',
  //           firstName: 'John',
  //           lastName: 'Doe',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 1,
  //       },
  //     },
  //   }).as('getUsers');

  //   cy.intercept('DELETE', '/api/orgs/users/remove', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       message: 'User removed successfully',
  //       removedUser: {
  //         id: 'user1',
  //         email: '<EMAIL>',
  //       },
  //     },
  //   }).as('removeUser');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Click remove button
  //   cy.findByTestId('remove-user-btn-user1').click();

  //   // Confirmation dialog should be visible
  //   cy.findByTestId('remove-user-modal').should('be.visible');
  //   cy.findByTestId('remove-confirmation-text').should('contain', '<EMAIL>');
  //   cy.findByTestId('remove-warning-text').should('be.visible');

  //   // Confirm removal
  //   cy.findByTestId('confirm-remove-btn').click();

  //   // Verify API call
  //   cy.wait('@removeUser').its('request.body').should('deep.include', {
  //     userId: 'user1',
  //   });

  //   // Should show success message
  //   cy.contains('User removed successfully').should('be.visible');

  //   // User should no longer be in table
  //   cy.findByTestId('member-row-user1').should('not.exist');
  // });

  // it('shows role hierarchy in permissions tab', () => {
  //   cy.visit('/team');
  //   cy.contains('Roles & Permissions').click();

  //   // Role hierarchy table should be visible
  //   cy.findByTestId('role-hierarchy-table').should('be.visible');

  //   // Check role levels
  //   cy.findByTestId('role-owner').should('be.visible');
  //   cy.findByTestId('role-superadmin').should('be.visible');
  //   cy.findByTestId('role-admin').should('be.visible');
  //   cy.findByTestId('role-manager').should('be.visible');
  //   cy.findByTestId('role-member').should('be.visible');
  //   cy.findByTestId('role-viewer').should('be.visible');

  //   // Check permissions display
  //   cy.findByTestId('permissions-owner').should('contain', 'Full control');
  //   cy.findByTestId('permissions-admin').should('contain', 'Team management');
  // });

  // it('handles role change validation', () => {
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'owner1',
  //           email: '<EMAIL>',
  //           firstName: 'Owner',
  //           lastName: 'User',
  //           role: 'Owner',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 1,
  //       },
  //     },
  //   }).as('getUsers');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Try to change owner role (should show warning)
  //   cy.findByTestId('change-role-btn-owner1').click();

  //   // Should show owner change warning
  //   cy.findByTestId('owner-change-warning').should('be.visible');
  //   cy.contains('Changing the Owner role').should('be.visible');

  //   // Should require confirmation
  //   cy.findByTestId('confirm-owner-change-checkbox').should('be.visible');
  //   cy.findByTestId('confirm-role-change-btn').should('be.disabled');

  //   // Check confirmation box
  //   cy.findByTestId('confirm-owner-change-checkbox').check();
  //   cy.findByTestId('confirm-role-change-btn').should('not.be.disabled');
  // });

  // it('displays role change history', () => {
  //   cy.visit('/team');
  //   cy.contains('Activity Log').click();

  //   // Filter by role changes
  //   cy.findByTestId('activity-filter-select').select('Role Changes');

  //   // Should show role change activities
  //   cy.findByTestId('activity-entry-role-change-1').should('be.visible');
  //   cy.findByTestId('activity-role-from').should('be.visible');
  //   cy.findByTestId('activity-role-to').should('be.visible');
  // });

  // it('handles bulk role changes', () => {
  //   // Stub multiple users
  //   cy.intercept('GET', '/api/orgs/*/users*', {
  //     statusCode: 200,
  //     body: {
  //       success: true,
  //       users: [
  //         {
  //           id: 'user1',
  //           email: '<EMAIL>',
  //           firstName: 'User',
  //           lastName: 'One',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //         {
  //           id: 'user2',
  //           email: '<EMAIL>',
  //           firstName: 'User',
  //           lastName: 'Two',
  //           role: 'Member',
  //           status: 'active',
  //           invitedAt: new Date().toISOString(),
  //           lastLogin: new Date().toISOString(),
  //         },
  //       ],
  //       pagination: {
  //         currentPage: 0,
  //         limit: 50,
  //         total: 2,
  //       },
  //     },
  //   }).as('getUsers');

  //   cy.visit('/team');
  //   cy.wait('@getUsers');

  //   // Select multiple users
  //   cy.findByTestId('select-user-user1').check();
  //   cy.findByTestId('select-user-user2').check();

  //   // Bulk actions menu should appear
  //   cy.findByTestId('bulk-actions-menu').should('be.visible');
  //   cy.findByTestId('bulk-change-role-btn').click();

  //   // Bulk role change modal
  //   cy.findByTestId('bulk-role-modal').should('be.visible');
  //   cy.findByTestId('selected-users-count').should('contain', '2 users selected');
  //   cy.findByTestId('bulk-role-select').select('Admin');

  //   // Confirm bulk change
  //   cy.findByTestId('confirm-bulk-role-btn').click();

  //   // Should show success
  //   cy.contains('Roles updated for 2 users').should('be.visible');
  // });
});
