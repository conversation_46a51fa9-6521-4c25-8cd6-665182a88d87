// Example: How to create a new team management test using the setup

import { 
  setupTeamManagementTest, 
  TEAM_MANAGEMENT_ASSUMPTIONS, 
  validateTeamManagementPrerequisites,
  getFirstAvailableTeamMember,
  checkUserPermissions 
} from './setup';

describe('Example Team Management Test', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Standard authentication setup
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
    
    // Log our assumptions
    cy.log(`Test user: ${email} (expected role: ${TEAM_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
  });

  it('example: validates user permissions', () => {
    // Use the setup function to navigate and validate
    setupTeamManagementTest();
    
    // Check specific permissions
    checkUserPermissions('invite_members');
    checkUserPermissions('change_roles');
    
    cy.log('✓ User has required permissions');
  });

  it('example: works with any team member', () => {
    cy.visit('/team');
    
    // Validate prerequisites first
    validateTeamManagementPrerequisites();
    
    // Get the first available team member dynamically
    getFirstAvailableTeamMember().then(($member) => {
      // Work with this member
      cy.wrap($member).should('be.visible');
      cy.log('Found team member to work with');
      
      // Example: Click on the member row
      cy.wrap($member).click();
    });
  });

  it('example: dynamic role testing', () => {
    cy.visit('/team');
    
    // Check if we have the expected organization structure
    const expectedRoles = TEAM_MANAGEMENT_ASSUMPTIONS.organization.availableRoles;
    cy.log(`Expected roles in org: ${expectedRoles.join(', ')}`);
    
    // Look for role indicators in the UI
    expectedRoles.forEach(role => {
      cy.get('body').then(($body) => {
        if ($body.text().includes(role)) {
          cy.log(`✓ Found ${role} role in organization`);
        }
      });
    });
  });
});
