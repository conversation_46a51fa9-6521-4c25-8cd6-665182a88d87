// Provider Management E2E Tests - Settings and Configuration
// Tests focus on provider editing, validation, and edge cases

describe('Provider Management - Settings and Configuration', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Login with real credentials to get valid session
    cy.session([userEmail, 'provider-settings'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Prevent infinite API calls by setting up global intercept
    cy.window().then((win) => {
      const orgId = win.localStorage.getItem('currentOrgId');
      if (orgId) {
        cy.intercept('GET', `/api/orgs/${orgId}/provider`, {
          statusCode: 404,
          body: { success: false, error: 'No provider found' }
        }).as('getOrgProvider');
      }
    });
  });

  afterEach(() => {
    // Clean up intercepts
    cy.intercept('GET', '/api/orgs/*/provider', (req) => {
      req.reply({ statusCode: 404, body: { success: false, error: 'No provider found' } });
    });
  });

  describe('Provider Information Management', () => {
    beforeEach(() => {
      // Set up existing provider for management tests
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');
    });

    it('should display current provider information', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Verify provider information is displayed
      cy.contains('Test Quantum Provider').should('be.visible');
      cy.contains('A leading quantum computing provider').should('be.visible');
      cy.contains('Public').should('be.visible');
      cy.contains('https://quantum-provider.com').should('be.visible');
      cy.contains('Edit Details').should('be.visible');
    });

    it('should allow editing all provider fields', () => {
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Updated Provider Name',
            providerDescription: 'Updated description with more details',
            status: 'private',
            about: 'https://updated-provider.com',
            updatedAt: new Date().toISOString()
          },
          metadata: { requestId: 'test-789', processingTime: '200ms' }
        }
      }).as('updateProvider');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Update all editable fields
      cy.get('input[placeholder*="Provider name"]')
        .clear().type('Updated Provider Name');
      cy.get('textarea[placeholder*="description"]')
        .clear().type('Updated description with more details');
      cy.get('input[placeholder*="website"]')
        .clear().type('https://updated-provider.com');
      cy.get('select').select('Private');

      // Save changes
      cy.contains('Save Changes').click();

      // Verify API call and response
      cy.wait('@updateProvider').then((interception) => {
        expect(interception.request.method).to.equal('PUT');
        expect(interception.request.body).to.include('name=Updated+Provider+Name');
        expect(interception.request.body).to.include('status=private');
      });

      // Verify success message
      cy.contains('Provider updated successfully').should('be.visible');

      // Verify updated information is displayed
      cy.contains('Updated Provider Name').should('be.visible');
      cy.contains('Updated description with more details').should('be.visible');
      cy.contains('Private').should('be.visible');
      cy.contains('https://updated-provider.com').should('be.visible');
    });

    it('should preserve optional fields when not modified', () => {
      // Set up provider with optional fields
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Provider',
          providerDescription: 'Test description',
          status: 'public',
          about: 'https://test-provider.com',
          supportEmail: '<EMAIL>',
          documentation: 'https://docs.test-provider.com',
          sdkLink: 'https://sdk.test-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProviderWithOptional');

      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Updated Provider Name',
            providerDescription: 'Test description',
            status: 'public',
            about: 'https://test-provider.com',
            supportEmail: '<EMAIL>',
            documentation: 'https://docs.test-provider.com',
            sdkLink: 'https://sdk.test-provider.com',
            updatedAt: new Date().toISOString()
          },
          metadata: { requestId: 'test-abc', processingTime: '150ms' }
        }
      }).as('updateProviderPreserve');

      cy.visit('/providers');
      cy.wait('@getOrgProviderWithOptional');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Only update name field
      cy.get('input[placeholder*="Provider name"]')
        .clear().type('Updated Provider Name');

      // Save changes
      cy.contains('Save Changes').click();

      // Verify optional fields are preserved
      cy.wait('@updateProviderPreserve').then((interception) => {
        expect(interception.request.body).to.include('name=Updated+Provider+Name');
        expect(interception.request.body).to.include('supportEmail=support%40test-provider.com');
        expect(interception.request.body).to.include('documentation=https%3A%2F%2Fdocs.test-provider.com');
      });

      // Verify success
      cy.contains('Provider updated successfully').should('be.visible');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      // Set up existing provider for validation tests
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');
    });

    it('should validate required fields on edit', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Clear required fields
      cy.get('input[placeholder*="Provider name"]').clear();
      cy.get('textarea[placeholder*="description"]').clear();
      cy.get('input[placeholder*="website"]').clear();

      // Try to save
      cy.contains('Save Changes').click();

      // Verify validation errors
      cy.contains('Provider name is required').should('be.visible');
      cy.contains('Description is required').should('be.visible');
      cy.contains('Website is required').should('be.visible');

      // Fill only name field
      cy.get('input[placeholder*="Provider name"]').type('Partial Provider');

      // Try to save again
      cy.contains('Save Changes').click();

      // Verify remaining validation errors
      cy.contains('Description is required').should('be.visible');
      cy.contains('Website is required').should('be.visible');
      cy.contains('Provider name is required').should('not.exist');
    });

    it('should validate provider name length and format', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Test name too long
      const longName = 'A'.repeat(101); // Exceeds 100 character limit
      cy.get('input[placeholder*="Provider name"]').clear().type(longName);
      cy.contains('Save Changes').click();

      // Verify length validation error
      cy.contains('Provider name must be less than 100 characters').should('be.visible');

      // Test valid name
      cy.get('input[placeholder*="Provider name"]').clear().type('Valid Provider Name');
      cy.contains('Save Changes').click();

      // Verify validation error is cleared
      cy.contains('Provider name must be less than 100 characters').should('not.exist');
    });

    it('should validate description length', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Test description too long
      const longDescription = 'A'.repeat(501); // Exceeds 500 character limit
      cy.get('textarea[placeholder*="description"]').clear().type(longDescription);
      cy.contains('Save Changes').click();

      // Verify length validation error
      cy.contains('Description must be less than 500 characters').should('be.visible');

      // Test valid description
      cy.get('textarea[placeholder*="description"]').clear().type('Valid description with reasonable length');
      cy.contains('Save Changes').click();

      // Verify validation error is cleared
      cy.contains('Description must be less than 500 characters').should('not.exist');
    });

    it('should validate URL formats', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Test invalid website URL
      cy.get('input[placeholder*="website"]').clear().type('not-a-valid-url');
      cy.contains('Save Changes').click();

      // Verify URL validation error
      cy.contains('Please enter a valid website URL').should('be.visible');

      // Test valid website URL
      cy.get('input[placeholder*="website"]').clear().type('https://valid-provider.com');
      cy.contains('Save Changes').click();

      // Verify validation error is cleared
      cy.contains('Please enter a valid website URL').should('not.exist');

      // Test invalid documentation URL (if field exists)
      cy.get('input[placeholder*="documentation"]').clear().type('invalid-doc-url');
      cy.contains('Save Changes').click();

      // Verify documentation URL validation
      cy.contains('Please enter a valid documentation URL').should('be.visible');

      // Test valid documentation URL
      cy.get('input[placeholder*="documentation"]').clear().type('https://docs.valid-provider.com');
      cy.contains('Save Changes').click();

      // Verify validation error is cleared
      cy.contains('Please enter a valid documentation URL').should('not.exist');
    });

    it('should validate email format for optional fields', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Test invalid support email
      cy.get('input[placeholder*="support email"]').clear().type('invalid-email');
      cy.contains('Save Changes').click();

      // Verify email validation error
      cy.contains('Please enter a valid email address').should('be.visible');

      // Test valid support email
      cy.get('input[placeholder*="support email"]').clear().type('<EMAIL>');
      cy.contains('Save Changes').click();

      // Verify validation error is cleared
      cy.contains('Please enter a valid email address').should('not.exist');

      // Test empty email (should be valid)
      cy.get('input[placeholder*="support email"]').clear();
      cy.contains('Save Changes').click();

      // Verify no validation error for empty optional field
      cy.contains('Please enter a valid email address').should('not.exist');
    });
  });

  describe('Status Management', () => {
    beforeEach(() => {
      // Set up existing provider for status tests
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');
    });

    it('should show confirmation when changing from public to private', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Change status from public to private
      cy.get('select').select('Private');

      // Verify confirmation dialog
      cy.contains('Changing from Public to Private').should('be.visible');
      cy.contains('This will make your provider invisible to users outside your organization').should('be.visible');
      cy.contains('Confirm Status Change').should('be.visible');
      cy.contains('Cancel').should('be.visible');

      // Cancel the change
      cy.contains('Cancel').click();

      // Verify status remains public
      cy.get('select').should('have.value', 'public');
    });

    it('should successfully change provider status to private', () => {
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Test Quantum Provider',
            providerDescription: 'A leading quantum computing provider',
            status: 'private',
            about: 'https://quantum-provider.com',
            updatedAt: new Date().toISOString()
          },
          metadata: { requestId: 'test-status', processingTime: '100ms' }
        }
      }).as('updateProviderStatus');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Change status from public to private
      cy.get('select').select('Private');

      // Confirm the change
      cy.contains('Confirm Status Change').click();

      // Save changes
      cy.contains('Save Changes').click();

      // Verify status update
      cy.wait('@updateProviderStatus');
      cy.contains('Provider updated successfully').should('be.visible');
      cy.contains('Private').should('be.visible');
    });

    it('should allow changing back from private to public', () => {
      // Set up provider with private status
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'private',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProviderPrivate');

      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Test Quantum Provider',
            providerDescription: 'A leading quantum computing provider',
            status: 'public',
            about: 'https://quantum-provider.com',
            updatedAt: new Date().toISOString()
          },
          metadata: { requestId: 'test-status-public', processingTime: '100ms' }
        }
      }).as('updateProviderStatusPublic');

      cy.visit('/providers');
      cy.wait('@getOrgProviderPrivate');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Change status from private to public
      cy.get('select').select('Public');

      // Save changes (no confirmation needed for private -> public)
      cy.contains('Save Changes').click();

      // Verify status update
      cy.wait('@updateProviderStatusPublic');
      cy.contains('Provider updated successfully').should('be.visible');
      cy.contains('Public').should('be.visible');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle concurrent edit conflicts', () => {
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'Original description',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');

      // Simulate conflict error
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 409,
        body: {
          success: false,
          error: 'Provider was modified by another user',
          conflict: true,
          currentData: {
            provider: 'Updated by Another User',
            providerDescription: 'Modified by concurrent user',
            status: 'public',
            about: 'https://quantum-provider.com'
          }
        }
      }).as('updateProviderConflict');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      cy.get('input[placeholder*="Provider name"]').clear().type('Conflicting Edit');
      cy.get('textarea[placeholder*="description"]').clear().type('Conflicting description');

      // Try to save
      cy.contains('Save Changes').click();

      // Verify conflict error handling
      cy.wait('@updateProviderConflict');
      cy.contains('Provider was modified by another user').should('be.visible');
      cy.contains('The latest changes are shown above').should('be.visible');
      cy.contains('Updated by Another User').should('be.visible');
      cy.contains('Modified by concurrent user').should('be.visible');
    });

    it('should handle network errors during save', () => {
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'Original description',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');

      // Simulate network error
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 500,
        body: {
          success: false,
          error: 'Network error occurred'
        }
      }).as('updateProviderError');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      cy.get('input[placeholder*="Provider name"]').clear().type('Network Test');

      // Try to save
      cy.contains('Save Changes').click();

      // Verify network error handling
      cy.wait('@updateProviderError');
      cy.contains('Failed to update provider').should('be.visible');
      cy.contains('Network error occurred').should('be.visible');
      cy.contains('Please try again').should('be.visible');
    });

    it('should prevent duplicate provider names', () => {
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'Original description',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');

      // Simulate duplicate name error
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 409,
        body: {
          success: false,
          error: 'Provider name already exists',
          field: 'name',
          existingProvider: {
            id: 'existing-provider',
            provider: 'Existing Provider Name'
          }
        }
      }).as('updateProviderDuplicate');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Try to use duplicate name
      cy.get('input[placeholder*="Provider name"]').clear().type('Existing Provider Name');

      // Try to save
      cy.contains('Save Changes').click();

      // Verify duplicate name error
      cy.wait('@updateProviderDuplicate');
      cy.contains('Provider name already exists').should('be.visible');
      cy.contains('Please choose a different name').should('be.visible');
      cy.contains('Existing Provider Name is already in use').should('be.visible');
    });

    it('should maintain form state after failed save', () => {
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'Original description',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');

      // Simulate validation error
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 400,
        body: {
          success: false,
          error: 'Validation failed',
          field: 'name',
          message: 'Provider name contains invalid characters'
        }
      }).as('updateProviderValidation');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      const newName = 'Invalid@Provider#Name';
      const newDescription = 'Updated description that should persist';
      
      cy.get('input[placeholder*="Provider name"]').clear().type(newName);
      cy.get('textarea[placeholder*="description"]').clear().type(newDescription);

      // Try to save
      cy.contains('Save Changes').click();

      // Verify validation error
      cy.wait('@updateProviderValidation');
      cy.contains('Provider name contains invalid characters').should('be.visible');

      // Verify form state is maintained
      cy.get('input[placeholder*="Provider name"]').should('have.value', newName);
      cy.get('textarea[placeholder*="description"]').should('have.value', newDescription);

      // User should be able to correct and resubmit
      cy.get('input[placeholder*="Provider name"]').clear().type('Valid Provider Name');
      cy.contains('Save Changes').click();

      // Should proceed with valid data
      cy.contains('Validation failed').should('not.exist');
    });

    it('should prevent infinite API calls during editing', () => {
      let apiCallCount = 0;
      
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'Test description',
          status: 'public',
          about: 'https://quantum-provider.com'
        }
      }).as('getOrgProviderInitial');

      // Count API calls during edit
      cy.intercept('PUT', '/api/providers/provider-1', (req) => {
        apiCallCount++;
        req.reply({
          statusCode: 200,
          body: {
            success: true,
            data: {
              id: 'provider-1',
              provider: 'Test Quantum Provider',
              providerDescription: 'Test description',
              status: 'public',
              about: 'https://quantum-provider.com',
              updatedAt: new Date().toISOString()
            }
          }
        });
      }).as('updateProviderCounted');

      cy.visit('/providers');
      cy.wait('@getOrgProviderInitial');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes and save multiple times
      cy.get('input[placeholder*="Provider name"]').clear().type('Test 1');
      cy.contains('Save Changes').click();
      
      cy.wait(500); // Wait between saves
      
      cy.get('input[placeholder*="Provider name"]').clear().type('Test 2');
      cy.contains('Save Changes').click();

      // Wait for any potential retries
      cy.wait(2000);

      // Verify reasonable number of API calls (should be 2)
      expect(apiCallCount).to.equal(2);
      
      // Verify modal is still functional
      cy.contains('Edit Provider').should('be.visible');
    });
  });

  describe('User Experience and Accessibility', () => {
    beforeEach(() => {
      // Set up existing provider
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }).as('getOrgProvider');
    });

    it('should provide clear feedback during form submission', () => {
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Updated Provider',
            providerDescription: 'Updated description',
            status: 'public',
            about: 'https://quantum-provider.com',
            updatedAt: new Date().toISOString()
          },
          metadata: { requestId: 'test-ux', processingTime: '150ms' }
        },
        delay: 1000 // Simulate slow response
      }).as('updateProviderSlow');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      cy.get('input[placeholder*="Provider name"]').clear().type('Updated Provider');

      // Save changes
      cy.contains('Save Changes').click();

      // Verify loading state
      cy.contains('Save Changes').should('be.disabled');
      cy.contains('Saving...').should('be.visible');

      // Wait for completion
      cy.wait('@updateProviderSlow');

      // Verify success feedback
      cy.contains('Save Changes').should('not.be.disabled');
      cy.contains('Saving...').should('not.exist');
      cy.contains('Provider updated successfully').should('be.visible');
    });

    it('should allow keyboard navigation and form submission', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal with keyboard (if possible)
      cy.contains('Edit Details').focus();
      cy.realPress('Enter'); // Try to open with Enter key

      // If modal opened, test keyboard navigation
      if (cy.contains('Edit Provider').should('be.visible')) {
        // Test tab navigation through form fields
        cy.get('input[placeholder*="Provider name"]').focus();
        cy.realPress('Tab');
        cy.get('textarea[placeholder*="description"]').should('be.focused');
        cy.realPress('Tab');
        cy.get('input[placeholder*="website"]').should('be.focused');
        cy.realPress('Tab');
        cy.get('select').should('be.focused');

        // Test form submission with Enter
        cy.get('select').focus();
        cy.realPress('Enter'); // Should not submit on select
        cy.contains('Save Changes').focus();
        cy.realPress('Enter'); // Should submit on button
      }
    });

    it('should have proper form labels and accessibility attributes', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Verify form labels are properly associated
      cy.get('label[for*="provider-name"]').should('exist');
      cy.get('label[for*="description"]').should('exist');
      cy.get('label[for*="website"]').should('exist');

      // Verify inputs have proper attributes
      cy.get('input[placeholder*="Provider name"]')
        .should('have.attr', 'type', 'text')
        .should('have.attr', 'required');
      
      cy.get('textarea[placeholder*="description"]')
        .should('have.attr', 'required');
      
      cy.get('input[placeholder*="website"]')
        .should('have.attr', 'type', 'url')
        .should('have.attr', 'required');

      // Verify buttons have proper aria labels
      cy.contains('Save Changes')
        .should('have.attr', 'type', 'submit');
      
      cy.contains('Cancel')
        .should('have.attr', 'type', 'button');
    });
  });
});