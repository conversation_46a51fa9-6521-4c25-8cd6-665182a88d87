# Quick Usage Guide - Provider Management Tests

## 🚀 Running Tests

```bash
# Run all provider management tests
npx cypress run --spec "cypress/e2e/provider-management/**/*.cy.ts"

# Run specific test files
npx cypress run --spec "cypress/e2e/provider-management/provider-creation.cy.ts"
npx cypress run --spec "cypress/e2e/provider-management/provider-editing.cy.ts"
npx cypress run --spec "cypress/e2e/provider-management/provider-navigation.cy.ts"

# Run with different user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=pass123 npx cypress run

# Open Cypress UI for debugging
npx cypress open
```

## 📝 Creating New Tests

### 1. Import the setup functions:
```typescript
import { 
  setupProviderManagementTest,           // Complete setup + navigation
  PROVIDER_MANAGEMENT_ASSUMPTIONS,       // Configuration object
  validateProviderManagementPrerequisites, // Validate before tests
  checkProviderStatus,                   // Check if org has provider
  openProviderForm,                      // Open provider modal
  fillProviderForm,                      // Fill form with test data
  checkProviderPermissions               // Check specific permissions
} from './setup';
```

### 2. Standard test structure:
```typescript
describe('Your Provider Test Suite', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Standard auth
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
    
    // Log assumptions
    cy.log(`User: ${email} (role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
  });

  it('your test', () => {
    // Setup and navigate to providers page
    setupProviderManagementTest();
    
    // Your test logic here...
  });
});
```

## 🔧 Helper Functions

### `checkProviderStatus()`
```typescript
// Check if organization has a provider
checkProviderStatus().then((status) => {
  if (status === 'has_provider') {
    // Test provider management features
  } else if (status === 'needs_provider') {
    // Test provider creation/selection
  }
});
```

### `openProviderForm()`
```typescript
// Open provider creation/edit modal
openProviderForm();

// Verify modal opened
cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');
```

### `fillProviderForm(data)`
```typescript
// Fill provider form with test data
const testData = fillProviderForm({
  name: 'My Test Provider',
  description: 'Test provider description',
  website: 'https://example.com'
});

// Returns the data that was filled for verification
cy.log(`Created provider: ${testData.name}`);
```

### `checkProviderPermissions(permission)`
```typescript
// Check if user has specific permissions
checkProviderPermissions('create_providers');
checkProviderPermissions('edit_providers');
checkProviderPermissions('manage_providers');
```

## 🎯 Common Test Patterns

### Pattern 1: Test based on provider status
```typescript
it('adapts to provider status', () => {
  setupProviderManagementTest();
  
  checkProviderStatus().then((status) => {
    if (status === 'has_provider') {
      // Test editing existing provider
      cy.get('button:contains("Edit")').click();
      // ... edit tests
    } else if (status === 'needs_provider') {
      // Test creating new provider
      openProviderForm();
      fillProviderForm({ name: 'New Provider' });
      // ... creation tests
    }
  });
});
```

### Pattern 2: Test form interactions
```typescript
it('handles provider form', () => {
  setupProviderManagementTest();
  openProviderForm();
  
  // Test form validation
  cy.get('button[type="submit"]').click();
  // Check for validation errors
  
  // Fill valid data
  const data = fillProviderForm({
    name: 'Valid Provider',
    description: 'Valid description',
    website: 'https://valid.com'
  });
  
  // Submit form
  cy.get('button[type="submit"]').click();
  
  // Verify success
  cy.contains('success', { timeout: 10_000 }).should('be.visible');
});
```

### Pattern 3: Test responsive behavior
```typescript
it('works on different screen sizes', () => {
  const viewports = [
    { width: 1200, height: 800, name: 'desktop' },
    { width: 768, height: 1024, name: 'tablet' },
    { width: 375, height: 667, name: 'mobile' }
  ];
  
  viewports.forEach(viewport => {
    cy.viewport(viewport.width, viewport.height);
    setupProviderManagementTest();
    
    // Test functionality on this viewport
    cy.contains('Provider Management').should('be.visible');
  });
});
```

### Pattern 4: Test permission-based features
```typescript
it('shows appropriate UI for permissions', () => {
  setupProviderManagementTest();
  
  // Check what user can see based on permissions
  cy.get('body').then(($body) => {
    const hasCreateButton = $body.find('button:contains("Create")').length > 0;
    const hasEditButton = $body.find('button:contains("Edit")').length > 0;
    
    if (hasCreateButton) {
      checkProviderPermissions('create_providers');
    }
    
    if (hasEditButton) {
      checkProviderPermissions('edit_providers');
    }
  });
});
```

## 🐛 Troubleshooting

### Test fails with "Provider Management not found"
```typescript
// Add this check at start of test
cy.visit('/providers');
cy.get('body').then(($body) => {
  if ($body.text().includes('Access Denied')) {
    cy.log('User lacks ManageProviders permission');
  }
});
```

### Form not opening
```typescript
// Debug provider form opening
cy.get('body').then(($body) => {
  const buttons = $body.find('button:contains("Create"), button:contains("Edit")');
  cy.log(`Found ${buttons.length} provider buttons`);
  
  if (buttons.length === 0) {
    cy.log('No provider management buttons found - check permissions');
  }
});
```

### Provider status unclear
```typescript
// Debug provider status detection
checkProviderStatus().then((status) => {
  cy.log(`Provider status: ${status}`);
  
  if (status === 'unknown') {
    cy.get('body').then(($body) => {
      cy.log('Page content:', $body.text().substring(0, 200));
    });
  }
});
```

## ✅ Best Practices

### 1. **Always check provider status first**
```typescript
// Don't assume organization state
checkProviderStatus().then((status) => {
  // Adapt test based on actual state
});
```

### 2. **Use dynamic test data**
```typescript
// Avoid hardcoded names that might conflict
const testData = fillProviderForm({
  name: `Test Provider ${Date.now()}`,
  description: `Test description ${Math.random()}`
});
```

### 3. **Handle async operations**
```typescript
// Wait for form submissions
cy.get('button[type="submit"]').click();
cy.contains('success', { timeout: 15_000 }).should('be.visible');
```

### 4. **Test both success and error paths**
```typescript
// Test validation
cy.get('button[type="submit"]').click(); // Empty form
cy.get('.error, [role="alert"]').should('exist');

// Test success
fillProviderForm({ /* valid data */ });
cy.get('button[type="submit"]').click();
cy.contains('success').should('be.visible');
```

### 5. **Clean up test data when possible**
```typescript
// Note: Provider cleanup may require manual intervention
// as providers are typically persistent resources
cy.log('Test provider created - may need manual cleanup');
```

## 📊 Environment Variables

```bash
# Required
TEST_USER_EMAIL=<EMAIL>    # User with Owner role
TEST_USER_PASSWORD=qBraid2!              # User password

# Optional
CYPRESS_baseUrl=http://localhost:3000    # App URL
```

## 🔍 Debugging Tips

1. **Use Cypress UI** for step-by-step debugging: `npx cypress open`
2. **Check browser console** for JavaScript errors
3. **Verify API calls** in Network tab
4. **Use cy.log()** extensively for debugging test flow
5. **Take screenshots** on failures: `cy.screenshot()`
