// Provider Navigation and UI Tests

import { 
  setupProviderManagementTest, 
  PROVIDER_MANAGEMENT_ASSUMPTIONS, 
  validateProviderManagementPrerequisites,
  checkProviderStatus,
  checkProviderPermissions
} from './setup';

/**
 * Provider Navigation and UI Tests
 * 
 * Prerequisites: User has Owner role with ManageProviders permission
 */
describe('Provider Management - Navigation and UI', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
    
    // Log assumptions for this test suite
    cy.log(`Test user: ${email} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
    cy.log('Testing: Provider navigation and UI functionality');
  });

  it('loads provider management page without errors', () => {
    setupProviderManagementTest();
    
    // Verify basic page structure
    cy.contains('Provider Management').should('be.visible');
    cy.url().should('include', '/providers');
    
    // Check for loading states completion
    cy.get('body').should('not.contain', 'Loading Provider...');
    
    // Verify no error messages
    cy.get('body').then(($body) => {
      const hasErrors = 
        $body.text().includes('Error') || 
        $body.text().includes('Failed') ||
        $body.text().includes('Something went wrong');
      
      if (!hasErrors) {
        cy.log('✓ Page loaded without error messages');
      } else {
        cy.log('WARNING: Error messages detected on page');
      }
    });
  });

  it('displays appropriate content based on provider status', () => {
    setupProviderManagementTest();
    
    checkProviderStatus().then((status) => {
      if (status === 'needs_provider') {
        // Should show onboarding flow
        cy.contains('Setup Your Provider').should('be.visible');
        cy.contains('Create Provider').should('be.visible');
        
        // Check for onboarding description
        cy.get('body').then(($body) => {
          const hasOnboardingText = 
            $body.text().includes('Connect with an existing provider') ||
            $body.text().includes('register your own quantum computing');
          
          if (hasOnboardingText) {
            cy.log('✓ Onboarding content displayed correctly');
          }
        });
        
      } else if (status === 'has_provider') {
        // Should show provider management interface
        cy.get('body').then(($body) => {
          const hasProviderInfo = 
            $body.find('[data-testid*="provider"], .provider-card, .provider-info').length > 0;
          
          if (hasProviderInfo) {
            cy.log('✓ Provider management interface displayed');
            
            // Check for provider status badges
            const hasStatusBadges = 
              $body.text().includes('Owner') || 
              $body.text().includes('Active') ||
              $body.text().includes('Public') ||
              $body.text().includes('Private');
            
            if (hasStatusBadges) {
              cy.log('✓ Provider status information displayed');
            }
          }
        });
      }
    });
  });

  it('shows correct permissions-based UI elements', () => {
    setupProviderManagementTest();
    
    // Check for permission-gated elements
    cy.get('body').then(($body) => {
      const hasManageButtons = 
        $body.find('button:contains("Create"), button:contains("Edit"), button:contains("Add")').length > 0;
      
      if (hasManageButtons) {
        cy.log('✓ Management buttons visible - user has proper permissions');
        checkProviderPermissions('manage_providers');
      } else {
        cy.log('No management buttons found - checking permission restrictions');
        
        // Look for permission denied messages
        const hasPermissionDenied = 
          $body.text().includes('Access Denied') ||
          $body.text().includes('Permission') ||
          $body.text().includes('not allowed');
        
        if (hasPermissionDenied) {
          cy.log('Permission restrictions detected');
        }
      }
    });
  });

  it('handles responsive design elements', () => {
    setupProviderManagementTest();
    
    // Test different viewport sizes
    const viewports = [
      { width: 1200, height: 800, name: 'desktop' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];
    
    viewports.forEach(viewport => {
      cy.viewport(viewport.width, viewport.height);
      cy.log(`Testing ${viewport.name} viewport (${viewport.width}x${viewport.height})`);
      
      // Verify page is still functional
      cy.contains('Provider Management').should('be.visible');
      
      // Check for responsive navigation
      cy.get('body').then(($body) => {
        const hasResponsiveElements = 
          $body.find('.responsive, .mobile, .tablet, .desktop').length > 0 ||
          $body.find('[class*="sm:"], [class*="md:"], [class*="lg:"]').length > 0;
        
        if (hasResponsiveElements) {
          cy.log(`✓ Responsive elements detected for ${viewport.name}`);
        }
      });
    });
    
    // Reset to default viewport
    cy.viewport(1280, 720);
  });

  it('displays provider cards with correct information', () => {
    setupProviderManagementTest();
    
    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Look for provider card/display
        cy.get('body').then(($body) => {
          const hasProviderDisplay = 
            $body.find('[data-testid*="provider"], .provider-card, .provider-info').length > 0;
          
          if (hasProviderDisplay) {
            cy.log('✓ Provider display found');
            
            // Check for provider information elements
            const providerElement = $body.find('[data-testid*="provider"], .provider-card, .provider-info').first();
            const providerText = providerElement.text();
            
            // Should contain provider name
            if (providerText.length > 0) {
              cy.log('✓ Provider information displayed');
              
              // Check for common provider info elements
              const hasProviderDetails = 
                providerText.includes('Owner') ||
                providerText.includes('Active') ||
                providerText.includes('Edit') ||
                providerText.includes('Website');
              
              if (hasProviderDetails) {
                cy.log('✓ Provider details and actions available');
              }
            }
          }
        });
      } else if (status === 'needs_provider') {
        // Check for provider selection cards in form
        cy.get('button:contains("Create Provider"), button:contains("Add Provider")').first().click();
        
        // Look for existing provider selection tab
        cy.get('body').then(($body) => {
          if ($body.find('[role="tab"]:contains("Select"), [role="tab"]:contains("Existing")').length > 0) {
            cy.get('[role="tab"]:contains("Select"), [role="tab"]:contains("Existing")').click();
            
            // Wait for providers to load
            cy.wait(2000);
            
            // Check for provider cards
            cy.get('body').then(($cardBody) => {
              const hasProviderCards = 
                $cardBody.find('.provider-card, [data-testid*="provider"], .card').length > 0;
              
              if (hasProviderCards) {
                cy.log('✓ Provider selection cards displayed');
                
                // Check first provider card for expected elements
                cy.get('.provider-card, [data-testid*="provider"], .card').first().then(($card) => {
                  const cardText = $card.text();
                  
                  const hasProviderInfo = 
                    cardText.includes('Select') ||
                    cardText.includes('Popular') ||
                    cardText.includes('Public') ||
                    cardText.includes('Website');
                  
                  if (hasProviderInfo) {
                    cy.log('✓ Provider cards contain expected information');
                  }
                });
              } else {
                cy.log('No provider cards found - may be loading or empty');
              }
            });
          }
        });
      }
    });
  });

  it('handles loading states appropriately', () => {
    // Intercept provider API calls to test loading states
    cy.intercept('GET', '/api/providers*', { delay: 2000 }).as('getProviders');
    cy.intercept('GET', '/api/orgs/*/provider*', { delay: 1000 }).as('getOrgProvider');
    
    cy.visit('/providers');
    
    // Check for loading indicators
    cy.get('body').then(($body) => {
      const hasLoadingIndicators = 
        $body.find('.animate-spin, .loading, .spinner').length > 0 ||
        $body.text().includes('Loading') ||
        $body.text().includes('Please wait');
      
      if (hasLoadingIndicators) {
        cy.log('✓ Loading indicators displayed during data fetch');
      }
    });
    
    // Wait for loading to complete
    cy.contains('Provider Management').should('be.visible');
    cy.log('✓ Loading states handled appropriately');
  });

  it('provides proper navigation and breadcrumbs', () => {
    setupProviderManagementTest();
    
    // Check for navigation elements
    cy.get('body').then(($body) => {
      // Look for breadcrumbs or navigation
      const hasNavigation = 
        $body.find('.breadcrumb, .nav, [data-testid*="nav"]').length > 0 ||
        $body.text().includes('Dashboard') ||
        $body.text().includes('Home');
      
      if (hasNavigation) {
        cy.log('✓ Navigation elements found');
      }
      
      // Check for back/home links
      const hasBackLinks = 
        $body.find('a[href="/"], a:contains("Dashboard"), a:contains("Home")').length > 0;
      
      if (hasBackLinks) {
        cy.log('✓ Back navigation available');
      }
    });
    
    // Verify URL structure
    cy.url().should('include', '/providers');
    cy.log('✓ Correct URL structure for provider management');
  });
});
