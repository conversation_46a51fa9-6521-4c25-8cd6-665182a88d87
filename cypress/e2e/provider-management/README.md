# Provider Management Tests

This directory contains end-to-end tests for provider management functionality in the partner dashboard.

## Test Assumptions

### User Role & Permissions
- **Test User**: `<EMAIL>` (or `TEST_USER_EMAIL` env var)
- **Required Role**: Owner
- **Required Permissions**:
  - `ManageProviders` - Can create and manage providers
  - `ManageDevices` - Can manage provider devices
  - `view_providers` - Can view provider information
  - `create_providers` - Can create new providers
  - `edit_providers` - Can edit existing providers
  - `assign_providers` - Can assign providers to organization

### Provider System Structure
- Organizations can have **one provider assigned** (new architecture)
- Provider creation supports both **custom providers** and **existing provider selection**
- Providers have statuses: `public`, `private`, `pending_approval`, `removed`
- Provider forms support **image uploads** for logos (light and dark themes)
- Provider editing is available for **owned providers**

### Expected UI Elements
- Provider Management page at `/providers`
- Create/Edit provider buttons with permission guards
- Provider form modal with tabs (Create Custom vs Select Existing)
- Image upload components for logos
- Provider information display with status badges
- Responsive design for different screen sizes

## Test Files

### `setup.ts`
Contains shared setup functions and assumptions validation:
- `PROVIDER_MANAGEMENT_ASSUMPTIONS` - Configuration object with all assumptions
- `validateProviderManagementPrerequisites()` - Validates assumptions before tests
- `setupProviderManagementTest()` - Common setup for provider management tests
- `checkProviderStatus()` - Determines if org has provider or needs setup
- `openProviderForm()` - Helper to open provider creation/edit modal
- `fillProviderForm()` - Helper to fill provider form with test data
- `checkProviderPermissions()` - Helper to verify user permissions

### `provider-creation.cy.ts`
Tests for provider creation functionality:
- Navigate to provider management page
- Open provider creation form
- Create custom provider with required fields
- Validate form field requirements
- Handle form cancellation
- Display existing providers for selection

### `provider-editing.cy.ts`
Tests for provider editing functionality:
- Display provider information for existing providers
- Open provider edit form
- Update provider information
- Handle additional/optional fields
- Test image upload components
- Validate edit form submission
- Cancel edits without saving

### `provider-navigation.cy.ts`
Tests for navigation and UI functionality:
- Load provider management page without errors
- Display appropriate content based on provider status
- Show correct permissions-based UI elements
- Handle responsive design
- Display provider cards with correct information
- Handle loading states
- Provide proper navigation and breadcrumbs

## Dynamic Test Approach

These tests are designed to be **dynamic** and **adaptive**:

✅ **Dynamic Approach**:
- Works with organizations that have or don't have providers
- Adapts to different provider statuses and permissions
- Uses flexible selectors for UI elements
- Handles both onboarding and management scenarios
- Tests real API interactions without mocks

❌ **Avoided Hardcoded Approach**:
- No specific provider IDs or names in test logic
- No mocked API responses with fixed data
- No assumptions about specific provider configurations

## Provider States Handled

### 1. **No Provider (Onboarding)**
- Shows "Setup Your Provider" interface
- Tests provider creation flow
- Tests existing provider selection

### 2. **Has Provider (Management)**
- Shows provider information and status
- Tests provider editing functionality
- Tests provider management features

### 3. **Permission Variations**
- Handles users with different permission levels
- Shows appropriate UI elements based on permissions
- Gracefully handles permission restrictions

## Running the Tests

```bash
# Run all provider management tests
npx cypress run --spec "cypress/e2e/provider-management/**/*.cy.ts"

# Run specific test file
npx cypress run --spec "cypress/e2e/provider-management/provider-creation.cy.ts"

# Run with custom test user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=yourpass npx cypress run
```

## Prerequisites Validation

Before each test suite runs, the setup validates:
1. User has provider management permissions (can access `/providers`)
2. Page loads without permission errors
3. Required UI elements are present and accessible
4. User can see management controls appropriate to their permissions

## Test Data Management

### Provider Creation
- Uses dynamic names with timestamps to avoid conflicts
- Tests with realistic provider information
- Validates required vs optional fields

### Provider Editing
- Works with existing provider data
- Tests incremental updates
- Validates form pre-population

### Image Uploads
- Tests file input components (without actual uploads)
- Validates upload UI elements
- Checks for light/dark theme logo support

## Troubleshooting

### "Access Denied" errors
- Verify the test user has Owner role
- Check that user has `ManageProviders` permission
- Ensure organization membership is active

### "No provider management controls visible"
- Check user permissions in organization settings
- Verify UI elements use expected selectors
- Check for permission guard components

### "Provider form not opening"
- Ensure provider management page loads correctly
- Check for JavaScript errors in browser console
- Verify modal/dialog components are functioning

### "Form submission failures"
- Check network connectivity for API calls
- Verify form validation is working
- Check browser console for API errors

## API Endpoints Tested

- `GET /api/providers` - Fetch available providers
- `POST /api/providers` - Create new provider
- `PATCH /api/providers/:id` - Update existing provider
- `GET /api/orgs/:orgId/provider` - Get organization's provider
- `PUT /api/orgs/:orgId/provider` - Assign provider to organization

## File Upload Testing

The tests validate file upload UI components but do not perform actual file uploads to avoid:
- S3 storage costs during testing
- Test data pollution
- Complex file cleanup requirements

Instead, tests verify:
- File input elements exist
- Upload UI components render correctly
- Form handles file selection appropriately
