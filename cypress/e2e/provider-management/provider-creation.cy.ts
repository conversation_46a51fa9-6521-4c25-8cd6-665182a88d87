// Provider Creation and Management Tests

import { 
  setupProviderManagementTest, 
  PROVIDER_MANAGEMENT_ASSUMPTIONS, 
  validateProviderManagementPrerequisites,
  checkProviderStatus,
  openProviderForm,
  fillProviderForm,
  checkProviderPermissions
} from './setup';

/**
 * Provider Creation Tests
 * 
 * Prerequisites: User has Owner role with ManageProviders permission
 */
describe('Provider Management - Provider Creation', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });
    
    // Log assumptions for this test suite
    cy.log(`Test user: ${email} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`);
    cy.log(`Required permissions: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.permissions.join(', ')}`);
  });

  it('navigates to provider management page successfully', () => {
    setupProviderManagementTest();
    
    // Verify page elements
    cy.contains('Provider Management').should('be.visible');
    cy.url().should('include', '/providers');
    
    // Check for expected UI elements based on provider status
    checkProviderStatus().then((status) => {
      if (status === 'needs_provider') {
        cy.contains('Setup Your Provider').should('be.visible');
        cy.contains('Create Provider').should('be.visible');
      } else if (status === 'has_provider') {
        cy.log('Organization already has a provider - testing edit functionality');
      }
    });
  });

  it('opens provider creation form', () => {
    setupProviderManagementTest();
    
    // Check permissions first
    checkProviderPermissions('create_providers');
    
    // Open the provider form
    openProviderForm();
    
    // Verify form modal opened
    cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');
    
    // Check for form elements
    cy.get('body').then(($body) => {
      const modalText = $body.text();
      
      // Should contain form-related text
      const hasFormContent = 
        modalText.includes('Provider') && 
        (modalText.includes('Create') || modalText.includes('Edit') || modalText.includes('Setup'));
      
      if (hasFormContent) {
        cy.log('✓ Provider form modal opened successfully');
      }
      
      // Look for tabs (Create Custom vs Select Existing)
      if ($body.find('[role="tablist"], .tabs').length > 0) {
        cy.log('✓ Provider form has tab interface');
        cy.get('[role="tab"], .tab').should('have.length.greaterThan', 0);
      }
    });
  });

  it('creates a custom provider with required fields', () => {
    setupProviderManagementTest();
    
    // Only proceed if organization needs a provider
    checkProviderStatus().then((status) => {
      if (status === 'needs_provider') {
        openProviderForm();
        
        // Select "Create Custom" tab if available
        cy.get('body').then(($body) => {
          if ($body.find('[role="tab"]:contains("Create"), [role="tab"]:contains("Custom")').length > 0) {
            cy.get('[role="tab"]:contains("Create"), [role="tab"]:contains("Custom")').click();
          }
        });
        
        // Fill the form with test data
        const testData = fillProviderForm({
          name: `Cypress Test Provider ${Date.now()}`,
          description: 'A quantum computing provider created by Cypress automated tests',
          website: 'https://cypress-test-provider.example.com'
        });
        
        // Submit the form
        cy.get('button[type="submit"], button:contains("Create"), button:contains("Submit")').click();
        
        // Wait for success (either redirect or success message)
        cy.get('body', { timeout: 15_000 }).then(($body) => {
          const bodyText = $body.text();
          
          if (bodyText.includes('success') || bodyText.includes('created') || bodyText.includes(testData.name)) {
            cy.log('✓ Provider created successfully');
          } else {
            cy.log('Provider creation may still be in progress');
          }
        });
        
        cy.log(`Created test provider: ${testData.name}`);
      } else {
        cy.log('Organization already has a provider - skipping creation test');
      }
    });
  });

  it('validates required fields in provider form', () => {
    setupProviderManagementTest();
    openProviderForm();
    
    // Select "Create Custom" tab if available
    cy.get('body').then(($body) => {
      if ($body.find('[role="tab"]:contains("Create"), [role="tab"]:contains("Custom")').length > 0) {
        cy.get('[role="tab"]:contains("Create"), [role="tab"]:contains("Custom")').click();
      }
    });
    
    // Try to submit empty form
    cy.get('button[type="submit"], button:contains("Create"), button:contains("Submit")').click();
    
    // Check for validation errors
    cy.get('body').then(($body) => {
      const hasValidationErrors = 
        $body.find('[role="alert"], .error, .invalid').length > 0 ||
        $body.text().includes('required') ||
        $body.text().includes('error');
      
      if (hasValidationErrors) {
        cy.log('✓ Form validation working - empty form rejected');
      } else {
        cy.log('Form validation may be handled differently');
      }
    });
    
    // Fill only name field and check validation
    cy.get('input[name="name"], input[placeholder*="name"]').type('Test Provider');
    cy.get('button[type="submit"], button:contains("Create")').click();
    
    // Should still show validation errors for missing required fields
    cy.wait(1000); // Allow time for validation
    cy.log('Tested partial form submission');
  });

  it('handles provider form cancellation', () => {
    setupProviderManagementTest();
    openProviderForm();
    
    // Fill some data
    cy.get('input[name="name"], input[placeholder*="name"]').type('Test Provider to Cancel');
    
    // Cancel the form
    cy.get('body').then(($body) => {
      if ($body.find('button:contains("Cancel")').length > 0) {
        cy.get('button:contains("Cancel")').click();
      } else if ($body.find('[data-testid*="close"], button[aria-label*="close"]').length > 0) {
        cy.get('[data-testid*="close"], button[aria-label*="close"]').first().click();
      } else {
        // Try pressing Escape key
        cy.get('body').type('{esc}');
      }
    });
    
    // Verify modal closed
    cy.get('[data-testid*="modal"], [role="dialog"]').should('not.exist');
    cy.log('✓ Provider form cancelled successfully');
  });

  it('displays existing providers for selection', () => {
    setupProviderManagementTest();
    
    checkProviderStatus().then((status) => {
      if (status === 'needs_provider') {
        openProviderForm();
        
        // Select "Select Existing" tab if available
        cy.get('body').then(($body) => {
          if ($body.find('[role="tab"]:contains("Select"), [role="tab"]:contains("Existing")').length > 0) {
            cy.get('[role="tab"]:contains("Select"), [role="tab"]:contains("Existing")').click();
            
            // Wait for providers to load
            cy.wait(2000);
            
            // Check for provider cards
            cy.get('body').then(($cardBody) => {
              const hasProviderCards = 
                $cardBody.find('.provider-card, [data-testid*="provider"], .card').length > 0;
              
              if (hasProviderCards) {
                cy.log('✓ Existing providers displayed for selection');
                
                // Check for provider information
                cy.get('.provider-card, [data-testid*="provider"], .card').first().then(($card) => {
                  const cardText = $card.text();
                  if (cardText.includes('Select') || cardText.includes('Choose')) {
                    cy.log('✓ Provider cards have selection options');
                  }
                });
              } else {
                cy.log('No existing providers available for selection');
              }
            });
          }
        });
      } else {
        cy.log('Organization already has a provider - skipping selection test');
      }
    });
  });
});
