// Provider Management E2E Tests - Provider Management
// Tests focus on provider editing and management functionality

import {
  PROVIDER_MANAGEMENT_ASSUMPTIONS,
  setupProviderManagementTest,
  openProviderForm,
  fillProviderForm,
} from './setup';

describe('Provider Management - Provider Management', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session([userEmail, 'provider-management'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Set up existing provider for management tests
    cy.intercept('GET', '/api/orgs/*/provider', {
      statusCode: 200,
      body: {
        id: 'provider-1',
        provider: 'Test Quantum Provider',
        providerDescription: 'A leading quantum computing provider',
        status: 'public',
        about: 'https://quantum-provider.com',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    }).as('getOrgProvider');

    // Log test assumptions
    cy.log(
      `Test user: ${userEmail} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
  });

  it('should open edit provider modal', () => {
    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Click edit button
    cy.contains('Edit Details').click();
    cy.wait(1000);
    // Verify modal is pre-filled with current data
    cy.contains('Edit Your Provider').should('be.visible');
    cy.get('input[name="name"]').should('have.value', 'Test Quantum Provider');
    cy.get('textarea[name="description"]').should(
      'have.value',
      'A leading quantum computing provider',
    );
    cy.get('input[name*="website"]').should('have.value', 'https://quantum-provider.com');

  });

  it('should update provider information successfully', () => {
    cy.intercept('PUT', '/api/providers/provider-1', {
      statusCode: 200,
      body: {
        success: true,
        data: {
          id: 'provider-1',
          provider: 'Updated Quantum Provider',
          providerDescription: 'Updated quantum computing services',
          status: 'private',
          about: 'https://updated-quantum.com',
          updatedAt: new Date().toISOString(),
        },
        metadata: { requestId: 'test-456', processingTime: '150ms' },
      },
    }).as('updateProvider');

    setupProviderManagementTest();
    cy.wait('@getOrgProvider');

    // Open edit modal
    openProviderForm();

    // Update provider information

    cy.get('input[name="name"]').clear().type('Updated Quantum Provider');
    cy.get('textarea[name="description"]').clear().type('Updated quantum computing services');
    cy.get('input[name="website"]').clear().type('https://updated-quantum.com');
    cy.get('select').select('Private');

    // Save changes
    cy.contains('Save Changes').click();

    // Verify API call
    cy.wait('@updateProvider').then((interception) => {
      expect(interception.request.method).to.equal('PUT');
      expect(interception.request.url).to.include('/api/providers/provider-1');
    });

    // Verify success message
    cy.contains('Provider updated successfully').should('be.visible');

    // Verify updated information is displayed
    cy.contains('Updated Quantum Provider').should('be.visible');
    cy.contains('Updated quantum computing services').should('be.visible');
    
  });

  // it('should cancel edit without saving changes', () => {
  //   setupProviderManagementTest();
  //   cy.wait('@getOrgProvider');

  //   // Open edit modal
  //   openProviderForm();

  //   // Make changes
  //   cy.get('input[placeholder*="Provider name"]').clear().type('Modified Name');
  //   cy.get('textarea[placeholder*="description"]').clear().type('Modified Description');

  //   // Cancel without saving
  //   cy.contains('Cancel').click();

  //   // Verify original data is still displayed
  //   cy.contains('Test Quantum Provider').should('be.visible');
  //   cy.contains('A leading quantum computing provider').should('be.visible');
  //   cy.contains('Modified Name').should('not.exist');
  // });

  // it('should validate required fields on edit', () => {
  //   setupProviderManagementTest();
  //   cy.wait('@getOrgProvider');

  //   // Open edit modal
  //   openProviderForm();

  //   // Clear required fields
  //   cy.get('input[placeholder*="Provider name"]').clear();
  //   cy.get('textarea[placeholder*="description"]').clear();
  //   cy.get('input[placeholder*="website"]').clear();

  //   // Try to save
  //   cy.contains('Save Changes').click();

  //   // Verify validation errors
  //   cy.contains('Provider name is required').should('be.visible');
  //   cy.contains('Description is required').should('be.visible');
  //   cy.contains('Website is required').should('be.visible');
  // });

  // it('should prevent provider deletion', () => {
  //   setupProviderManagementTest();
  //   cy.wait('@getOrgProvider');

  //   // Verify no delete option exists
  //   cy.contains('Delete Provider').should('not.exist');
  //   cy.contains('Remove Provider').should('not.exist');
  //   cy.contains('Deactivate Provider').should('not.exist');

  //   // Open edit modal
  //   openProviderForm();

  //   // Verify no delete option in edit form
  //   cy.contains('Delete').should('not.exist');
  //   cy.contains('Remove').should('not.exist');
  //   cy.contains('Deactivate').should('not.exist');

  //   // Only save and cancel options should be available
  //   cy.contains('Save Changes').should('be.visible');
  //   cy.contains('Cancel').should('be.visible');
  // });

  // it('should handle concurrent edit conflicts', () => {
  //   cy.intercept('PUT', '/api/providers/provider-1', {
  //     statusCode: 409,
  //     body: {
  //       success: false,
  //       error: 'Provider was modified by another user',
  //       conflict: true,
  //       currentData: {
  //         provider: 'Concurrent Edit Provider',
  //         providerDescription: 'Modified by another user',
  //       },
  //     },
  //   }).as('updateProviderConflict');

  //   setupProviderManagementTest();
  //   cy.wait('@getOrgProvider');

  //   // Open edit modal
  //   openProviderForm();

  //   // Make changes
  //   cy.get('input[placeholder*="Provider name"]').clear().type('Conflicting Edit');

  //   // Try to save
  //   cy.contains('Save Changes').click();

  //   // Verify conflict error message
  //   cy.wait('@updateProviderConflict');
  //   cy.contains('Provider was modified by another user').should('be.visible');
  //   cy.contains('The latest changes are shown above').should('be.visible');
  //   cy.contains('Concurrent Edit Provider').should('be.visible');
  // });

  // it('should handle API errors during update', () => {
  //   cy.intercept('PUT', '/api/providers/provider-1', {
  //     statusCode: 500,
  //     body: {
  //       success: false,
  //       error: 'Failed to update provider',
  //       message: 'Internal server error',
  //     },
  //   }).as('updateProviderError');

  //   setupProviderManagementTest();
  //   cy.wait('@getOrgProvider');

  //   // Open edit modal
  //   openProviderForm();

  //   // Make changes
  //   cy.get('input[placeholder*="Provider name"]').clear().type('Error Update');

  //   // Try to save
  //   cy.contains('Save Changes').click();

  //   // Verify error handling
  //   cy.wait('@updateProviderError');
  //   cy.contains('Failed to update provider').should('be.visible');
  // });
});
