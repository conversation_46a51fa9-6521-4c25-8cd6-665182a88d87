// Provider Editing and Management Tests

import {
  setupProviderManagementTest,
  PROVIDER_MANAGEMENT_ASSUMPTIONS,
  validateProviderManagementPrerequisites,
  checkProviderStatus,
  openProviderForm,
  fillProviderForm,
  checkProviderPermissions,
} from './setup';

/**
 * Provider Editing Tests
 *
 * Prerequisites: User has Owner role with ManageProviders permission
 * Organization should have a provider assigned for editing tests
 */
describe('Provider Management - Provider Editing', () => {
  const email = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const password = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    cy.session(['ui-login', email], () => {
      cy.loginViaUI(email, password);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Log assumptions for this test suite
    cy.log(
      `Test user: ${email} (expected role: ${PROVIDER_MANAGEMENT_ASSUMPTIONS.currentUser.role})`,
    );
    cy.log('Testing: Provider editing and management functionality');
  });

  it('displays provider information when organization has a provider', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Look for provider information display
        cy.get('body').then(($body) => {
          const hasProviderInfo =
            $body.find('[data-testid*="provider"], .provider-card, .provider-info').length > 0;

          if (hasProviderInfo) {
            cy.log('✓ Provider information displayed');

            // Check for provider details
            const bodyText = $body.text();
            if (
              bodyText.includes('Owner') ||
              bodyText.includes('Active') ||
              bodyText.includes('Edit')
            ) {
              cy.log('✓ Provider status and ownership information shown');
            }

            // Look for edit button
            const hasEditButton =
              $body.find('button:contains("Edit"), [data-testid*="edit"]').length > 0;

            if (hasEditButton) {
              cy.log('✓ Edit provider button available');
            }
          }
        });
      } else {
        cy.log('Organization does not have a provider - skipping display test');
      }
    });
  });

  it('opens provider edit form for existing provider', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Check edit permissions
        checkProviderPermissions('edit_providers');

        // Click edit button
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();

        // Verify edit form opened
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Check for edit-specific content
        cy.get('body').then(($body) => {
          const modalText = $body.text();

          if (modalText.includes('Edit') || modalText.includes('Update')) {
            cy.log('✓ Provider edit form opened');

            // Check if form is pre-populated
            cy.get('input[name="name"], input[placeholder*="name"]').should('not.have.value', '');
            cy.log('✓ Edit form pre-populated with existing data');
          }
        });
      } else {
        cy.log('Organization does not have a provider - skipping edit test');
      }
    });
  });

  it('updates provider information successfully', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Open edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Get current provider name for reference
        cy.get('input[name="name"], input[placeholder*="name"]')
          .invoke('val')
          .then((currentName) => {
            const updatedName = `${currentName} - Updated ${Date.now()}`;

            // Update provider information
            const testData = fillProviderForm({
              name: updatedName,
              description: 'Updated description for automated testing',
              website: 'https://updated-provider.example.com',
            });

            // Submit the update
            cy.get(
              'button[type="submit"], button:contains("Update"), button:contains("Save")',
            ).click();

            // Wait for success indication
            cy.get('body', { timeout: 15_000 }).then(($body) => {
              const bodyText = $body.text();

              if (
                bodyText.includes('success') ||
                bodyText.includes('updated') ||
                bodyText.includes(testData.name)
              ) {
                cy.log('✓ Provider updated successfully');
              } else {
                cy.log('Provider update may still be in progress');
              }
            });

            cy.log(`Updated provider name to: ${testData.name}`);
          });
      } else {
        cy.log('Organization does not have a provider - skipping update test');
      }
    });
  });

  it('handles additional fields in provider edit form', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Open edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Look for additional fields toggle
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("Additional"), button:contains("Optional")').length > 0) {
            cy.get('button:contains("Additional"), button:contains("Optional")').click();
            cy.log('✓ Additional fields section expanded');

            // Check for optional fields
            const optionalFields = ['supportEmail', 'documentation', 'sdkLink'];
            optionalFields.forEach((field) => {
              cy.get('body').then(($fieldBody) => {
                if ($fieldBody.find(`input[name="${field}"]`).length > 0) {
                  cy.get(`input[name="${field}"]`).clear().type(`test-${field}@example.com`);
                  cy.log(`✓ Updated ${field} field`);
                }
              });
            });
          } else {
            cy.log('Additional fields may be always visible or not available');
          }
        });
      } else {
        cy.log('Organization does not have a provider - skipping additional fields test');
      }
    });
  });

  it('handles image upload in provider edit form', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Open edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Look for image upload components
        cy.get('body').then(($body) => {
          const hasImageUpload =
            $body.find('input[type="file"], [data-testid*="upload"], .upload').length > 0;

          if (hasImageUpload) {
            cy.log('✓ Image upload components found');

            // Check for logo upload sections
            if ($body.text().includes('Logo') || $body.text().includes('Image')) {
              cy.log('✓ Logo upload sections available');

              // Test file input existence (don't actually upload in tests)
              cy.get('input[type="file"]').should('exist');
              cy.log('✓ File input elements present');
            }
          } else {
            cy.log('Image upload components not found or not visible');
          }
        });
      } else {
        cy.log('Organization does not have a provider - skipping image upload test');
      }
    });
  });

  it('validates provider edit form submission', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Open edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Clear required field and try to submit
        cy.get('input[name="name"], input[placeholder*="name"]').clear();
        cy.get('button[type="submit"], button:contains("Update"), button:contains("Save")').click();

        // Check for validation errors
        cy.get('body').then(($body) => {
          const hasValidationErrors =
            $body.find('[role="alert"], .error, .invalid').length > 0 ||
            $body.text().includes('required') ||
            $body.text().includes('error');

          if (hasValidationErrors) {
            cy.log('✓ Edit form validation working - empty required field rejected');
          } else {
            cy.log('Edit form validation may be handled differently');
          }
        });

        // Restore the name field
        cy.get('input[name="name"], input[placeholder*="name"]').type('Restored Provider Name');
        cy.log('✓ Tested edit form validation');
      } else {
        cy.log('Organization does not have a provider - skipping validation test');
      }
    });
  });

  it('cancels provider edit without saving changes', () => {
    setupProviderManagementTest();

    checkProviderStatus().then((status) => {
      if (status === 'has_provider') {
        // Open edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('[data-testid*="modal"], [role="dialog"]').should('be.visible');

        // Make some changes
        cy.get('input[name="name"], input[placeholder*="name"]')
          .clear()
          .type('Changed Name - Should Not Save');

        // Cancel the form
        cy.get('body').then(($body) => {
          if ($body.find('button:contains("Cancel")').length > 0) {
            cy.get('button:contains("Cancel")').click();
          } else if ($body.find('[data-testid*="close"], button[aria-label*="close"]').length > 0) {
            cy.get('[data-testid*="close"], button[aria-label*="close"]').first().click();
          } else {
            // Try pressing Escape key
            cy.get('body').type('{esc}');
          }
        });

        // Verify modal closed
        cy.get('[data-testid*="modal"], [role="dialog"]').should('not.exist');

        // Verify changes were not saved by reopening edit form
        cy.get('button:contains("Edit"), [data-testid*="edit"]').first().click();
        cy.get('input[name="name"], input[placeholder*="name"]').should(
          'not.have.value',
          'Changed Name - Should Not Save',
        );

        cy.log('✓ Provider edit cancelled without saving changes');
      } else {
        cy.log('Organization does not have a provider - skipping cancel test');
      }
    });
  });
});
