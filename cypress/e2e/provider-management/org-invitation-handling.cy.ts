// Organization Invitation Acceptance Tests
// Tests for handling org invitations and ensuring proper org context

describe('Organization Invitation Acceptance', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Login with real credentials
    cy.session([userEmail, 'org-invitation'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
    });
  });

  it('should handle pending organization invitation', () => {
    // Stub permissions to show pending invitation
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'member',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
          }
        }
      }
    }).as('getPermissions');

    cy.visit('/dashboard');
    cy.wait('@getPermissions');

    // Verify pending invitation is shown
    cy.contains('Pending Organization Invitation').should('be.visible');
    cy.contains('Pending Organization').should('be.visible');
    cy.contains('You have been invited to join').should('be.visible');
    cy.contains('Accept Invitation').should('be.visible');
    cy.contains('Decline Invitation').should('be.visible');
  });

  it('should prevent provider management access with pending invitation', () => {
    // Stub permissions with pending org invitation
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'member',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      }
    }).as('getPermissions');

    // Stub org provider call to simulate pending state
    cy.intercept('GET', '/api/orgs/pending-org-123/provider', {
      statusCode: 403,
      body: {
        success: false,
        error: 'Organization invitation pending acceptance'
      }
    }).as('getOrgProvider');

    cy.visit('/providers');
    cy.wait(['@getPermissions', '@getOrgProvider']);

    // Should show invitation pending message instead of provider management
    cy.contains('Organization Invitation Pending').should('be.visible');
    cy.contains('Please accept the invitation to access provider management').should('be.visible');
    cy.contains('Accept Invitation').should('be.visible');
    cy.contains('View Invitations').should('be.visible');
    
    // Should NOT show provider management interface
    cy.contains('Provider Management').should('not.exist');
    cy.contains('Setup Your Provider').should('not.exist');
    cy.contains('Edit Details').should('not.exist');
  });

  it('should allow accepting organization invitation', () => {
    // Initial state with pending invitation
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'member',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      }
    }).as('getPermissionsInitial');

    // Accept invitation API call
    cy.intercept('POST', '/api/orgs/pending-org-123/accept', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Invitation accepted successfully',
        org: {
          id: 'pending-org-123',
          name: 'Pending Organization',
          role: 'member',
          status: 'active',
          joinedAt: new Date().toISOString()
        }
      }
    }).as('acceptInvitation');

    // Updated permissions after acceptance
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices', 'manage:providers'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'member',
            status: 'active',
            updated: new Date().toISOString(),
            permissions: ['view:devices', 'manage:providers'],
            joinedAt: new Date().toISOString()
          }
        }
      }
    }).as('getPermissionsAfterAccept');

    cy.visit('/dashboard');
    cy.wait('@getPermissionsInitial');

    // Accept the invitation
    cy.contains('Accept Invitation').click();
    cy.wait('@acceptInvitation');

    // Verify success message
    cy.contains('Invitation accepted successfully').should('be.visible');
    cy.contains('You are now a member of Pending Organization').should('be.visible');

    // Wait for permissions to update
    cy.wait('@getPermissionsAfterAccept');

    // Verify org is now active in org switcher
    cy.contains('Pending Organization').should('be.visible');
    cy.contains('Active').should('be.visible');
  });

  it('should allow declining organization invitation', () => {
    // Initial state with pending invitation
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'member',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      }
    }).as('getPermissionsInitial');

    // Decline invitation API call
    cy.intercept('POST', '/api/orgs/pending-org-123/decline', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Invitation declined successfully'
      }
    }).as('declineInvitation');

    // Updated permissions after decline
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {} // No org roles after declining
      }
    }).as('getPermissionsAfterDecline');

    cy.visit('/dashboard');
    cy.wait('@getPermissionsInitial');

    // Decline the invitation
    cy.contains('Decline Invitation').click();
    cy.wait('@declineInvitation');

    // Verify success message
    cy.contains('Invitation declined successfully').should('be.visible');

    // Wait for permissions to update
    cy.wait('@getPermissionsAfterDecline');

    // Verify org is no longer in org switcher
    cy.contains('Pending Organization').should('not.exist');
  });

  it('should handle expired organization invitation', () => {
    const expiredDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // 1 day ago

    // Stub permissions with expired invitation
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'expired-org-123': {
            orgId: 'expired-org-123',
            orgName: 'Expired Organization',
            role: 'member',
            status: 'expired',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
            invitationExpires: expiredDate
          }
        }
      }
    }).as('getPermissions');

    cy.visit('/dashboard');
    cy.wait('@getPermissions');

    // Verify expired invitation message
    cy.contains('Expired Invitation').should('be.visible');
    cy.contains('Expired Organization').should('be.visible');
    cy.contains('This invitation has expired').should('be.visible');
    cy.contains('Request New Invitation').should('be.visible');
    cy.contains('Remove').should('be.visible');

    // Should not allow accepting expired invitation
    cy.contains('Accept Invitation').should('not.exist');
  });

  it('should show multiple pending invitations', () => {
    // Stub permissions with multiple pending invitations
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-1': {
            orgId: 'pending-org-1',
            orgName: 'First Pending Org',
            role: 'member',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['view:devices'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          'pending-org-2': {
            orgId: 'pending-org-2',
            orgName: 'Second Pending Org',
            role: 'admin',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['manage:providers'],
            invitationSent: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            invitationExpires: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      }
    }).as('getPermissions');

    cy.visit('/dashboard');
    cy.wait('@getPermissions');

    // Verify multiple invitations are shown
    cy.contains('Pending Organization Invitations').should('be.visible');
    cy.contains('You have 2 pending invitations').should('be.visible');
    
    cy.contains('First Pending Org').should('be.visible');
    cy.contains('Member').should('be.visible');
    cy.contains('Accept Invitation').should('be.visible');
    
    cy.contains('Second Pending Org').should('be.visible');
    cy.contains('Admin').should('be.visible');
    cy.contains('Accept Invitation').should('be.visible');
  });

  it('should prevent provider creation with pending invitation', () => {
    // Stub permissions with pending invitation but provider management permissions
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['manage:providers'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {
          'pending-org-123': {
            orgId: 'pending-org-123',
            orgName: 'Pending Organization',
            role: 'admin',
            status: 'pending',
            updated: new Date().toISOString(),
            permissions: ['manage:providers'],
            invitationSent: new Date().toISOString(),
            invitationExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      }
    }).as('getPermissions');

    // Even with provider management permissions, should block due to pending invitation
    cy.intercept('GET', '/api/orgs/pending-org-123/provider', {
      statusCode: 403,
      body: {
        success: false,
        error: 'Organization invitation pending acceptance'
      }
    }).as('getOrgProvider');

    cy.visit('/providers');
    cy.wait(['@getPermissions', '@getOrgProvider']);

    // Should show invitation pending message despite having permissions
    cy.contains('Organization Invitation Pending').should('be.visible');
    cy.contains('Please accept the invitation to access provider management').should('be.visible');
    
    // Should NOT allow provider creation
    cy.contains('Create Provider').should('not.exist');
    cy.contains('Setup Your Provider').should('not.exist');
  });

  it('should handle org context cleanup after declining invitation', () => {
    // Set up localStorage with org ID from declined invitation
    cy.window().then((win) => {
      win.localStorage.setItem('currentOrgId', 'declined-org-123');
    });

    // Stub permissions after declining
    cy.intercept('GET', '/api/auth/permissions*', {
      statusCode: 200,
      body: {
        success: true,
        permissions: ['view:devices'],
        roles: ['member'],
        timestamp: new Date().toISOString(),
        orgRoles: {} // No org roles
      }
    }).as('getPermissions');

    cy.visit('/providers');
    cy.wait('@getPermissions');

    // Should handle missing org context gracefully
    cy.contains('Select an Organization').should('be.visible');
    cy.contains('No organization selected').should('be.visible');
    cy.contains('Please select an organization from the switcher above').should('be.visible');

    // Verify localStorage was cleaned up
    cy.window().then((win) => {
      expect(win.localStorage.getItem('currentOrgId')).to.be.null;
    });
  });
});