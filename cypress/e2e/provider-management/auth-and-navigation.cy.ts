// Provider Management E2E Tests - Auth and Navigation
// Tests focus on core user journeys and happy path scenarios

describe('Provider Management - Auth and Navigation', () => {
  const userEmail = Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const userPassword = Cypress.env('TEST_USER_PASSWORD') || 'qBraid2!';

  beforeEach(() => {
    // Login with real credentials to get valid session
    cy.session([userEmail, 'provider-management'], () => {
      cy.loginViaUI(userEmail, userPassword);
      cy.ensureAppSessionReady();
      cy.ensureOrgContext();
    });

    // Prevent infinite API calls by setting up global intercept
    cy.window().then((win) => {
      const orgId = win.localStorage.getItem('currentOrgId');
      if (orgId) {
        cy.intercept('GET', `/api/orgs/${orgId}/provider`, {
          statusCode: 404,
          body: { success: false, error: 'No provider found' },
        }).as('getOrgProvider');
      }
    });
  });

  afterEach(() => {
    // Clean up intercepts
    cy.intercept('GET', '/api/orgs/*/provider', (req) => {
      req.reply({ statusCode: 404, body: { success: false, error: 'No provider found' } });
    });
  });

  describe('Page Access & Navigation', () => {
    it('should navigate to provider management page', () => {
      cy.visit('/providers');

      // Verify page loads successfully
      cy.contains('Provider Management').should('be.visible');
      cy.url().should('include', '/providers');
    });

    it('should show onboarding flow when no provider exists', () => {
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 404,
        body: { success: false, error: 'No provider found' },
      }).as('getOrgProvider');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Verify onboarding elements
      cy.contains('Setup Your Provider').should('be.visible');
      cy.contains('Create Provider').should('be.visible');
      cy.contains('Set up your hardware provider').should('be.visible');
    });

    it('should show provider dashboard when provider exists', () => {
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      }).as('getOrgProvider');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Verify provider dashboard elements
      cy.contains('Test Quantum Provider').should('be.visible');
      cy.contains('A leading quantum computing provider').should('be.visible');
      cy.contains('Public').should('be.visible');
      cy.contains('Edit Details').should('be.visible');
      cy.contains('https://quantum-provider.com').should('be.visible');
    });

    it('should handle access denied for unauthorized users', () => {
      // Stub permissions without provider management access
      cy.intercept('GET', '/api/auth/permissions*', {
        statusCode: 200,
        body: {
          success: true,
          permissions: ['view:devices'],
          roles: ['member'],
          timestamp: new Date().toISOString(),
          orgRoles: {},
        },
      });

      cy.visit('/providers');

      // Verify access denied message
      cy.contains('Access Denied').should('be.visible');
      cy.contains('provider management permissions').should('be.visible');
      cy.contains('Return to Dashboard').should('be.visible');
    });
  });

  describe('Provider Creation Flow', () => {
    it('should open create provider modal', () => {
      cy.visit('/providers');

      // Click create button
      cy.contains('Create Provider').click();

      // Verify modal elements
      cy.contains('Create Provider').should('be.visible');
      cy.get('input[placeholder*="Provider name"]').should('be.visible');
      cy.get('textarea[placeholder*="description"]').should('be.visible');
      cy.get('input[placeholder*="website"]').should('be.visible');
      cy.contains('Cancel').should('be.visible');
      cy.contains('Create Provider').should('be.visible');
    });

    it('should create new provider successfully', () => {
      // Set up API intercepts
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 404,
        body: { success: false, error: 'No provider found' },
      }).as('getOrgProviderInitial');

      cy.intercept('POST', '/api/providers', {
        statusCode: 201,
        body: {
          success: true,
          data: {
            id: 'new-provider',
            provider: 'New Quantum Provider',
            providerDescription: 'Innovative quantum solutions',
            status: 'private',
            about: 'https://new-quantum.com',
            createdAt: new Date().toISOString(),
          },
          metadata: { requestId: 'test-123', processingTime: '100ms' },
        },
      }).as('createProvider');

      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'new-provider',
          provider: 'New Quantum Provider',
          providerDescription: 'Innovative quantum solutions',
          status: 'private',
          about: 'https://new-quantum.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      }).as('getOrgProviderAfterCreate');

      cy.visit('/providers');
      cy.wait('@getOrgProviderInitial');

      // Open create modal
      cy.contains('Create Provider').click();

      // Fill form with valid data
      cy.get('input[placeholder*="Provider name"]').type('New Quantum Provider');
      cy.get('textarea[placeholder*="description"]').type('Innovative quantum solutions');
      cy.get('input[placeholder*="website"]').type('https://new-quantum.com');

      // Submit form
      cy.contains('Create Provider').click();

      // Verify API call
      cy.wait('@createProvider').then((interception) => {
        expect(interception.request.method).to.equal('POST');
        expect(interception.request.body).to.include('name=New+Quantum+Provider');
        expect(interception.request.body).to.include('description=Innovative+quantum+solutions');
      });

      // Verify success message
      cy.contains('Provider created successfully').should('be.visible');

      // Verify redirect to dashboard
      cy.wait('@getOrgProviderAfterCreate');
      cy.contains('New Quantum Provider').should('be.visible');
    });

    it('should validate required fields on creation', () => {
      cy.visit('/providers');
      cy.contains('Create Provider').click();

      // Try to submit without filling required fields
      cy.contains('Create Provider').click();

      // Verify validation errors
      cy.contains('Provider name is required').should('be.visible');
      cy.contains('Description is required').should('be.visible');
      cy.contains('Website is required').should('be.visible');

      // Fill only name field
      cy.get('input[placeholder*="Provider name"]').type('Test Provider');

      // Try to submit again
      cy.contains('Create Provider').click();

      // Verify remaining validation errors
      cy.contains('Description is required').should('be.visible');
      cy.contains('Website is required').should('be.visible');
      cy.contains('Provider name is required').should('not.exist');
    });

    it('should validate URL format on creation', () => {
      cy.visit('/providers');
      cy.contains('Create Provider').click();

      // Fill all fields except invalid URL
      cy.get('input[placeholder*="Provider name"]').type('Test Provider');
      cy.get('textarea[placeholder*="description"]').type('Test description');
      cy.get('input[placeholder*="website"]').type('invalid-url');

      // Try to submit
      cy.contains('Create Provider').click();

      // Verify URL validation error
      cy.contains('Please enter a valid website URL').should('be.visible');

      // Fix URL
      cy.get('input[placeholder*="website"]').clear().type('https://test-provider.com');

      // Try to submit again
      cy.contains('Create Provider').click();

      // Verify validation error is cleared
      cy.contains('Please enter a valid website URL').should('not.exist');
    });
  });

  describe('Provider Management Flow', () => {
    beforeEach(() => {
      // Set up existing provider for management tests
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: {
          id: 'provider-1',
          provider: 'Test Quantum Provider',
          providerDescription: 'A leading quantum computing provider',
          status: 'public',
          about: 'https://quantum-provider.com',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      }).as('getOrgProvider');
    });

    it('should open edit provider modal', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Click edit button
      cy.contains('Edit Details').click();

      // Verify modal is pre-filled with current data
      cy.contains('Edit Provider').should('be.visible');
      cy.get('input[placeholder*="Provider name"]').should('have.value', 'Test Quantum Provider');
      cy.get('textarea[placeholder*="description"]').should(
        'have.value',
        'A leading quantum computing provider',
      );
      cy.get('input[placeholder*="website"]').should('have.value', 'https://quantum-provider.com');
      cy.get('select').should('have.value', 'public');
    });

    it('should update provider information successfully', () => {
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 200,
        body: {
          success: true,
          data: {
            id: 'provider-1',
            provider: 'Updated Quantum Provider',
            providerDescription: 'Updated quantum computing services',
            status: 'private',
            about: 'https://updated-quantum.com',
            updatedAt: new Date().toISOString(),
          },
          metadata: { requestId: 'test-456', processingTime: '150ms' },
        },
      }).as('updateProvider');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Update provider information
      cy.get('input[placeholder*="Provider name"]').clear().type('Updated Quantum Provider');
      cy.get('textarea[placeholder*="description"]')
        .clear()
        .type('Updated quantum computing services');
      cy.get('input[placeholder*="website"]').clear().type('https://updated-quantum.com');
      cy.get('select').select('Private');

      // Save changes
      cy.contains('Save Changes').click();

      // Verify API call
      cy.wait('@updateProvider').then((interception) => {
        expect(interception.request.method).to.equal('PUT');
        expect(interception.request.url).to.include('/api/providers/provider-1');
        expect(interception.request.body).to.include('name=Updated+Quantum+Provider');
      });

      // Verify success message
      cy.contains('Provider updated successfully').should('be.visible');

      // Verify updated information is displayed
      cy.contains('Updated Quantum Provider').should('be.visible');
      cy.contains('Updated quantum computing services').should('be.visible');
      cy.contains('Private').should('be.visible');
    });

    it('should handle status change confirmation', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Change status from public to private
      cy.get('select').select('Private');

      // Verify confirmation dialog appears
      cy.contains('Changing from Public to Private').should('be.visible');
      cy.contains('This will make your provider invisible').should('be.visible');
      cy.contains('Confirm Status Change').should('be.visible');
      cy.contains('Cancel').should('be.visible');

      // Cancel the status change
      cy.contains('Cancel').click();

      // Verify status remains unchanged
      cy.get('select').should('have.value', 'public');
      cy.contains('Confirm Status Change').should('not.exist');
    });

    it('should cancel edit without saving changes', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      cy.get('input[placeholder*="Provider name"]').clear().type('Modified Name');
      cy.get('textarea[placeholder*="description"]').clear().type('Modified Description');

      // Cancel without saving
      cy.contains('Cancel').click();

      // Verify original data is still displayed
      cy.contains('Test Quantum Provider').should('be.visible');
      cy.contains('A leading quantum computing provider').should('be.visible');
      cy.contains('Modified Name').should('not.exist');
    });

    it('should prevent provider deletion', () => {
      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Verify no delete option exists
      cy.contains('Delete Provider').should('not.exist');
      cy.contains('Remove Provider').should('not.exist');
      cy.contains('Deactivate Provider').should('not.exist');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Verify no delete option in edit form
      cy.contains('Delete').should('not.exist');
      cy.contains('Remove').should('not.exist');
      cy.contains('Deactivate').should('not.exist');

      // Only save and cancel options should be available
      cy.contains('Save Changes').should('be.visible');
      cy.contains('Cancel').should('be.visible');
    });
  });

  describe('Error Handling & Edge Cases', () => {
    it('should handle loading states', () => {
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 200,
        body: { id: 'test-provider', provider: 'Test Provider' },
        delay: 2000, // Simulate slow response
      }).as('getProviderSlow');

      cy.visit('/providers');

      // Verify loading state
      cy.contains('Loading providers...').should('be.visible');

      // Wait for response
      cy.wait('@getProviderSlow');

      // Verify provider data loads
      cy.contains('Test Provider').should('be.visible');
      cy.contains('Loading providers...').should('not.exist');
    });

    it('should handle API errors gracefully', () => {
      cy.intercept('GET', '/api/orgs/*/provider', {
        statusCode: 500,
        body: { success: false, error: 'Internal server error' },
      }).as('getProviderError');

      cy.visit('/providers');
      cy.wait('@getProviderError');

      // Verify error state
      cy.contains('Provider Error').should('be.visible');
      cy.contains('Unable to load provider information').should('be.visible');
      cy.contains('Refresh Page').should('be.visible');
    });

    it('should handle concurrent edit conflicts', () => {
      cy.intercept('PUT', '/api/providers/provider-1', {
        statusCode: 409,
        body: {
          success: false,
          error: 'Provider was modified by another user',
          conflict: true,
          currentData: {
            provider: 'Concurrent Edit Provider',
            providerDescription: 'Modified by another user',
          },
        },
      }).as('updateProviderConflict');

      cy.visit('/providers');
      cy.wait('@getOrgProvider');

      // Open edit modal
      cy.contains('Edit Details').click();

      // Make changes
      cy.get('input[placeholder*="Provider name"]').clear().type('Conflicting Edit');

      // Try to save
      cy.contains('Save Changes').click();

      // Verify conflict error message
      cy.wait('@updateProviderConflict');
      cy.contains('Provider was modified by another user').should('be.visible');
      cy.contains('The latest changes are shown above').should('be.visible');
      cy.contains('Concurrent Edit Provider').should('be.visible');
    });

    it('should prevent infinite API calls', () => {
      let apiCallCount = 0;

      cy.intercept('GET', '/api/orgs/*/provider', (req) => {
        apiCallCount++;
        req.reply({
          statusCode: 404,
          body: { success: false, error: 'No provider found' },
        });
      }).as('getOrgProviderCounted');

      cy.visit('/providers');

      // Wait for any potential retries
      cy.wait(3000);

      // Verify reasonable number of API calls
      expect(apiCallCount).to.be.lte(3);

      // Verify page is still functional
      cy.contains('Setup Your Provider').should('be.visible');
    });
  });
});
